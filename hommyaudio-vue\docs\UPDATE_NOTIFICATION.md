# 网页更新通知方案

## 方案对比

### 1. plugin-web-update-notification（已采用）

**优点：**
- ✅ 资源占用最小（<2KB）
- ✅ 接入简单，零业务代码修改
- ✅ 智能检测机制
- ✅ 用户体验好

**检测时机：**
- 首次加载页面
- 定时轮询（5分钟）
- 标签页切换回来时（visibilitychange）
- JS资源加载失败时

### 2. WebSocket 方案

**优点：**
- ✅ 实时性最强
- ✅ 精确控制

**缺点：**
- ❌ 需要后端配合
- ❌ 资源消耗大
- ❌ 维护成本高

### 3. Service Worker 方案

**优点：**
- ✅ 功能强大
- ✅ 支持离线

**缺点：**
- ❌ 复杂度高
- ❌ 可能造成缓存问题
- ❌ 调试困难

## 当前配置

```typescript
webUpdateNotice({
  logVersion: true,
  checkInterval: 5 * 60 * 1000, // 5分钟检查一次
  notificationProps: {
    title: '🔄 发现新版本',
    description: '检测到网站有更新，点击刷新获取最新内容',
    buttonText: '立即刷新',
    dismissButtonText: '稍后提醒',
  },
})
```

## 工作原理

1. 打包时生成 `version.json` 文件（包含git commit hash）
2. 客户端定期检查服务器上的版本号
3. 版本不匹配时显示更新通知
4. 用户点击刷新按钮更新页面

## 性能影响

- **文件大小**：注入代码 < 2KB
- **网络请求**：每5分钟请求一次小JSON文件
- **内存占用**：几乎可忽略
- **CPU占用**：几乎可忽略

## 自定义配置选项

```typescript
interface Options {
  // 检查间隔（毫秒）
  checkInterval?: number
  
  // 是否在控制台打印版本信息
  logVersion?: boolean
  
  // 通知UI配置
  notificationProps?: {
    title?: string
    description?: string
    buttonText?: string
    dismissButtonText?: string
  }
  
  // 自定义更新行为
  onUpdated?: (version: string) => void
}
```
