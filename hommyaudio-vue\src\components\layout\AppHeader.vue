<template>
  <a-layout-header class="app-header">
    <div class="header-content">
      <div class="logo-section">
        <img :src="logoUrl" alt="HommyAudio Logo" class="logo-image" @error="handleLogoError" />
      </div>

      <div class="nav-section">
        <a-menu
          mode="horizontal"
          :selected-keys="['home']"
          class="header-menu"
          @menu-item-click="handleMenuClick"
        >
          <a-menu-item key="home">{{ t('nav.home') }}</a-menu-item>
          <a-menu-item key="about" :selectable="false">{{ t('nav.about') }}</a-menu-item>
        </a-menu>
      </div>

      <div class="actions-section">
        <a-space size="medium">
          <!-- 主题切换 -->
          <a-dropdown @select="handleThemeSelect" trigger="hover">
            <a-button type="text" size="small" @click="handleThemeToggle" class="theme-toggle-btn">
              <template #icon>
                <icon-sun v-if="!isDark" />
                <icon-moon v-else />
              </template>
            </a-button>
            <template #content>
              <a-doption v-for="option in themeOptions" :key="option.value" :value="option.value">
                <template #icon>
                  <icon-sun v-if="option.value === 'light'" />
                  <icon-moon v-else-if="option.value === 'dark'" />
                  <icon-computer v-else />
                </template>
                {{ option.label }}
              </a-doption>
            </template>
          </a-dropdown>

          <!-- 语言切换 -->
          <a-button type="text" size="small" @click="toggleLanguage">
            <template #icon>
              <icon-language />
            </template>
            {{ isZhCN ? 'English' : '中文' }}
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 关于模态框 -->
    <AboutModal ref="aboutModalRef" />
  </a-layout-header>
</template>

<script setup>
import { ref } from 'vue'
import { IconSun, IconMoon, IconComputer, IconLanguage } from '@arco-design/web-vue/es/icon'
import { useTheme } from '@/composables/useTheme'
import { useI18n } from '@/composables/useI18n'
import AboutModal from '@/components/common/AboutModal.vue'
import logoUrl from '@/assets/logo.png'

const { themeOptions, setThemeMode, toggleTheme, isDark } = useTheme()
const { t, toggleLanguage, isZhCN } = useI18n()

const aboutModalRef = ref(null)

const handleMenuClick = (key) => {
  if (key === 'about') {
    aboutModalRef.value?.show()
  }
}

const handleThemeSelect = (value) => {
  setThemeMode(value)
}

const handleThemeToggle = () => {
  toggleTheme()
}

// Logo错误处理
const handleLogoError = (event) => {
  console.warn('Logo加载失败:', event.target.src)
}
</script>

<style scoped>
.app-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0;
  height: 64px;
  line-height: 64px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 100%;
}

.nav-section {
  flex: 1;
  display: flex;
  justify-content: center;
}

.actions-section {
  display: flex;
  align-items: center;
}

.theme-toggle-btn {
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.theme-toggle-btn:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

[arco-theme='dark'] .theme-toggle-btn:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-image {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  object-fit: contain;
  transition: filter 0.3s ease;
}

/* 深色模式下的logo处理 */
[arco-theme='dark'] .logo-image {
  /* 如果没有深色版本的logo，可以使用滤镜来调整亮度 */
  filter: brightness(1.1) contrast(0.9);
}

.header-menu {
  border-bottom: none;
  background: transparent;
}

/* 深色模式样式 */
[arco-theme='dark'] .app-header {
  background: #17171a;
  border-bottom: 1px solid #2e2e30;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
</style>
