<template>
  <div class="instructions-container">
    <a-card :title="t('instructions.title')" :bordered="false" class="instructions-card">
      <a-space direction="vertical" size="large" style="width: 100%">
        <!-- 基本使用 -->
        <div class="instruction-section">
          <a-typography-title :level="4">
            <IconInfoCircle /> {{ t('instructions.basic') }}
          </a-typography-title>
          <a-list size="small">
            <a-list-item>
              <IconCheckCircle class="check-icon" />
              {{ t('instructions.basic_step1') }}
            </a-list-item>
            <a-list-item>
              <IconCheckCircle class="check-icon" />
              {{ t('instructions.basic_step2') }}
            </a-list-item>
            <a-list-item>
              <IconCheckCircle class="check-icon" />
              {{ t('instructions.basic_step3') }}
            </a-list-item>
            <a-list-item>
              <IconCheckCircle class="check-icon" />
              {{ t('instructions.basic_step4') }}
            </a-list-item>
          </a-list>
        </div>

        <!-- 批量播放 -->
        <div class="instruction-section">
          <a-typography-title :level="4">
            <IconList /> {{ t('instructions.batch') }}
          </a-typography-title>
          <a-list size="small">
            <a-list-item>
              <IconCheckCircle class="check-icon" />
              {{ t('instructions.batch_step1') }}
            </a-list-item>
            <a-list-item>
              <IconCheckCircle class="check-icon" />
              {{ t('instructions.batch_step2') }}
            </a-list-item>
            <a-list-item>
              <IconCheckCircle class="check-icon" />
              {{ t('instructions.batch_step3') }}
            </a-list-item>
            <a-list-item>
              <IconCheckCircle class="check-icon" />
              {{ t('instructions.batch_step4') }}
            </a-list-item>
          </a-list>
        </div>

        <!-- 支持格式 -->
        <div class="instruction-section">
          <a-typography-title :level="4">
            <IconFile /> {{ t('instructions.formats') }}
          </a-typography-title>
          <a-space wrap>
            <a-tag color="blue">MP3</a-tag>
            <a-tag color="green">WAV</a-tag>
            <a-tag color="orange">OGG</a-tag>
            <a-tag color="purple">M4A</a-tag>
            <a-tag color="cyan">FLAC</a-tag>
            <a-tag color="red">WebM Audio</a-tag>
            <a-tag color="gold">{{ t('instructions.encrypted_links') }}</a-tag>
          </a-space>
        </div>
      </a-space>
    </a-card>
  </div>
</template>

<script setup>
import { IconInfoCircle, IconCheckCircle, IconList, IconFile } from '@arco-design/web-vue/es/icon'
import { useI18n } from '@/composables/useI18n'

const { t } = useI18n()
</script>

<style scoped>
.instructions-container {
  margin-bottom: 24px;
}

.instructions-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 深色模式下的说明卡片强调 */
[arco-theme='dark'] .instructions-card {
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: var(--bg-primary);
}

.instruction-section {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.instruction-section:last-child {
  border-bottom: none;
}

/* 深色模式下的分割线 */
[arco-theme='dark'] .instruction-section {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.check-icon {
  color: #00b42a;
  margin-right: 8px;
}

.instruction-section .arco-typography-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.instruction-section .arco-list-item {
  padding: 4px 0;
}
</style>
