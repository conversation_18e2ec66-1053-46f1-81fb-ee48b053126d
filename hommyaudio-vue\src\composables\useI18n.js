import { ref, computed } from 'vue'

// 当前语言
const currentLanguage = ref('zh-CN')

// 从localStorage读取语言设置
const savedLanguage = localStorage.getItem('hommyaudio-language')
if (savedLanguage) {
  currentLanguage.value = savedLanguage
} else {
  // 检测浏览器语言
  const browserLanguage = navigator.language || navigator.userLanguage
  currentLanguage.value = browserLanguage.startsWith('zh') ? 'zh-CN' : 'en-US'
}

// 翻译文本
const translations = {
  'zh-CN': {
    // 导航栏
    'nav.home': '首页',
    'nav.about': '关于',

    // 音频播放器
    'audio.title': '音频播放器',
    'audio.placeholder': '请输入音频URL (支持.mp3等格式及加密链接)',
    'audio.play': '播放',
    'audio.pause': '暂停',
    'audio.previous': '上一首',
    'audio.next': '下一首',
    'audio.volume': '音量',
    'audio.mute': '静音',
    'audio.loading': '正在加载音频...',
    'audio.error': '音频播放出错',
    'audio.retry': '重试',
    'audio.playing': '正在播放',

    // 播放模式
    'mode.single': '单曲播放',
    'mode.list': '列表播放',
    'mode.loop': '循环播放',
    'mode.batch': '批量模式',
    'mode.single_mode': '单个模式',

    // 播放列表
    'playlist.title': '播放列表',
    'playlist.clear': '清空列表',
    'playlist.add': '添加到播放列表',
    'playlist.batch_placeholder': '请输入多个音频URL，每行一个',
    'playlist.duration': '时长',
    'playlist.unknown': '未知',
    'playlist.audio_item': '音频',
    'playlist.playing': '正在播放',
    'playlist.played': '已播放',

    // 使用说明
    'instructions.title': '使用说明',
    'instructions.basic': '基本使用',
    'instructions.batch': '批量播放',
    'instructions.shortcuts': '快捷键',
    'instructions.formats': '支持的音频格式',
    'instructions.basic_step1': '在输入框中粘贴音频URL地址',
    'instructions.basic_step2': '点击"播放"按钮或按Enter键开始播放',
    'instructions.basic_step3': '支持所有浏览器兼容的音频格式 (MP3、WAV、OGG等)',
    'instructions.basic_step4': '支持加密音频链接 (如: https://hommyaudio.com/encrypt/xxxxx)',
    'instructions.batch_step1': '切换到"批量模式"可添加多个音频到播放列表',
    'instructions.batch_step2': '每行输入一个音频URL，支持一次性添加多个',
    'instructions.batch_step3': '使用"上一首"/"下一首"按钮控制播放顺序',
    'instructions.batch_step4': '支持单曲播放、列表播放、循环播放模式',
    'instructions.encrypted_links': '加密链接',

    // 快捷键
    'shortcuts.space': '播放/暂停',
    'shortcuts.enter': '播放当前URL',
    'shortcuts.left': '快退5秒',
    'shortcuts.right': '快进5秒',
    'shortcuts.up': '音量+10%',
    'shortcuts.down': '音量-10%',
    'shortcuts.space_key': '空格键',
    'shortcuts.enter_key': 'Enter键',
    'shortcuts.left_key': '← 方向键',
    'shortcuts.right_key': '→ 方向键',
    'shortcuts.up_key': '↑ 方向键',
    'shortcuts.down_key': '↓ 方向键',

    // 控制按钮
    'controls.previous': '上一首',
    'controls.next': '下一首',
    'controls.play': '播放',
    'controls.pause': '暂停',
    'controls.mute': '静音',
    'controls.loading_audio': '正在加载音频...',
    'controls.now_playing': '正在播放',
    'controls.retry': '重试',
    'controls.audio_load_failed': '音频加载失败',
    'controls.check_url_network': '请检查音频URL是否正确或网络连接是否正常',
    'controls.autoplay_blocked': '播放失败：浏览器阻止了自动播放，请点击播放按钮手动开始',
    'controls.play_failed': '播放失败，请检查音频文件是否有效',
    'controls.speed': '播放速度',

    // 页脚
    'footer.copyright': '© 2025 格鲁曼 All rights reserved.',
    'footer.icp': '蜀ICP备2023004112号',
    'footer.dark_mode': '深色模式',
    'footer.light_mode': '浅色模式',
    'footer.language': '语言',

    // 关于页面
    'about.title': '关于 HommyAudio',
    'about.description':
      '一个现代化的在线音频播放器，支持多种音频格式和加密链接播放，提供简洁优雅的用户体验。',
    'about.features': '主要功能',
    'about.feature1': '支持多种音频格式 (MP3、WAV、OGG、M4A、FLAC等)',
    'about.feature2': '支持加密音频链接播放',
    'about.feature3': '批量播放列表管理',
    'about.feature4': '键盘快捷键控制',
    'about.feature5': '深色模式和多语言支持',
    'about.tech_stack': '技术栈',
    'about.contact': '联系方式',
    'about.email': '邮箱',
  },

  'en-US': {
    // Navigation
    'nav.home': 'Home',
    'nav.about': 'About',

    // Audio Player
    'audio.title': 'Audio Player',
    'audio.placeholder': 'Enter audio URL (supports .mp3 and encrypted links)',
    'audio.play': 'Play',
    'audio.pause': 'Pause',
    'audio.previous': 'Previous',
    'audio.next': 'Next',
    'audio.volume': 'Volume',
    'audio.mute': 'Mute',
    'audio.loading': 'Loading audio...',
    'audio.error': 'Audio playback error',
    'audio.retry': 'Retry',
    'audio.playing': 'Now Playing',

    // Play Mode
    'mode.single': 'Single',
    'mode.list': 'List',
    'mode.loop': 'Loop',
    'mode.batch': 'Batch Mode',
    'mode.single_mode': 'Single Mode',

    // Playlist
    'playlist.title': 'Playlist',
    'playlist.clear': 'Clear List',
    'playlist.add': 'Add to Playlist',
    'playlist.batch_placeholder': 'Enter multiple audio URLs, one per line',
    'playlist.duration': 'Duration',
    'playlist.unknown': 'Unknown',
    'playlist.audio_item': 'Audio',
    'playlist.playing': 'Playing',
    'playlist.played': 'Played',

    // Instructions
    'instructions.title': 'Instructions',
    'instructions.basic': 'Basic Usage',
    'instructions.batch': 'Batch Playback',
    'instructions.shortcuts': 'Shortcuts',
    'instructions.formats': 'Supported Formats',
    'instructions.basic_step1': 'Paste audio URL in the input field',
    'instructions.basic_step2': 'Click "Play" button or press Enter to start playback',
    'instructions.basic_step3':
      'Supports all browser-compatible audio formats (MP3, WAV, OGG, etc.)',
    'instructions.basic_step4':
      'Supports encrypted audio links (e.g., https://hommyaudio.com/encrypt/xxxxx)',
    'instructions.batch_step1': 'Switch to "Batch Mode" to add multiple audios to playlist',
    'instructions.batch_step2': 'Enter one audio URL per line, supports adding multiple at once',
    'instructions.batch_step3': 'Use "Previous"/"Next" buttons to control playback order',
    'instructions.batch_step4': 'Supports single, list, and loop playback modes',
    'instructions.encrypted_links': 'Encrypted Links',

    // Shortcuts
    'shortcuts.space': 'Play/Pause',
    'shortcuts.enter': 'Play Current URL',
    'shortcuts.left': 'Seek -5s',
    'shortcuts.right': 'Seek +5s',
    'shortcuts.up': 'Volume +10%',
    'shortcuts.down': 'Volume -10%',
    'shortcuts.space_key': 'Space',
    'shortcuts.enter_key': 'Enter',
    'shortcuts.left_key': '← Arrow',
    'shortcuts.right_key': '→ Arrow',
    'shortcuts.up_key': '↑ Arrow',
    'shortcuts.down_key': '↓ Arrow',

    // Controls
    'controls.previous': 'Previous',
    'controls.next': 'Next',
    'controls.play': 'Play',
    'controls.pause': 'Pause',
    'controls.mute': 'Mute',
    'controls.loading_audio': 'Loading audio...',
    'controls.now_playing': 'Now Playing',
    'controls.retry': 'Retry',
    'controls.audio_load_failed': 'Audio loading failed',
    'controls.check_url_network':
      'Please check if the audio URL is correct or network connection is normal',
    'controls.autoplay_blocked':
      'Playback failed: Browser blocked autoplay, please click play button to start manually',
    'controls.play_failed': 'Playback failed, please check if the audio file is valid',
    'controls.speed': 'Playback Speed',

    // Footer
    'footer.copyright': '© 2025 Grumman All rights reserved.',
    'footer.icp': 'ICP: 蜀ICP备2023004112号',
    'footer.dark_mode': 'Dark Mode',
    'footer.light_mode': 'Light Mode',
    'footer.language': 'Language',

    // About Page
    'about.title': 'About HommyAudio',
    'about.description':
      'A modern online audio player that supports multiple audio formats and encrypted link playbook, providing a clean and elegant user experience.',
    'about.features': 'Key Features',
    'about.feature1': 'Support for multiple audio formats (MP3, WAV, OGG, M4A, FLAC, etc.)',
    'about.feature2': 'Support for encrypted audio link playback',
    'about.feature3': 'Batch playlist management',
    'about.feature4': 'Keyboard shortcut controls',
    'about.feature5': 'Dark mode and multi-language support',
    'about.tech_stack': 'Tech Stack',
    'about.contact': 'Contact',
    'about.email': 'Email',
  },
}

export const useI18n = () => {
  const t = (key) => {
    return translations[currentLanguage.value]?.[key] || key
  }

  const toggleLanguage = () => {
    currentLanguage.value = currentLanguage.value === 'zh-CN' ? 'en-US' : 'zh-CN'
    localStorage.setItem('hommyaudio-language', currentLanguage.value)
  }

  const isZhCN = computed(() => currentLanguage.value === 'zh-CN')

  return {
    currentLanguage,
    t,
    toggleLanguage,
    isZhCN,
  }
}
