{"name": "hommyaudio-vue", "version": "2.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "format": "prettier --write src/"}, "dependencies": {"@arco-design/web-vue": "^2.57.0", "pinia": "^3.0.3", "vue": "^3.5.17"}, "devDependencies": {"@plugin-web-update-notification/vite": "^2.0.0", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^8.0.4", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^6.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}