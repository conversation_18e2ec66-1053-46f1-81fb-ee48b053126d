import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

interface Track {
  url: string
  title: string
  duration: number | null
  played?: boolean // 是否已播放过
}

type PlayMode = 'single' | 'list' | 'loop'

interface PlaybackState {
  isPlaying?: boolean
  currentTime?: number
  duration?: number
  volume?: number
}

export const useAudioStore = defineStore('audio', () => {
  // 状态
  const currentUrl = ref<string>('')
  const playlist = ref<Track[]>([])
  const currentIndex = ref<number>(-1)
  const isPlaying = ref<boolean>(false)
  const currentTime = ref<number>(0)
  const duration = ref<number>(0)
  const volume = ref<number>(1)
  const playMode = ref<PlayMode>('single')
  const playbackRate = ref<number>(1)

  // 计算属性
  const hasPlaylist = computed(() => playlist.value.length > 0)
  const canPlayNext = computed(() => {
    if (playMode.value === 'loop') return hasPlaylist.value
    return currentIndex.value < playlist.value.length - 1
  })
  const canPlayPrevious = computed(() => {
    if (playMode.value === 'loop') return hasPlaylist.value
    return currentIndex.value > 0
  })
  const currentTrack = computed(() => {
    if (currentIndex.value >= 0 && currentIndex.value < playlist.value.length) {
      return playlist.value[currentIndex.value]
    }
    return null
  })

  // 动作
  const setCurrentUrl = (url: string) => {
    currentUrl.value = url
  }

  const addToPlaylist = (urls: string[]) => {
    const newTracks: Track[] = urls.map((url: string, index: number) => ({
      url,
      title: `音频 ${playlist.value.length + index + 1}`,
      duration: null,
      played: false
    }))
    playlist.value.push(...newTracks)
  }

  const removeFromPlaylist = (index: number) => {
    if (index === currentIndex.value) {
      // 如果删除的是当前播放项，停止播放
      isPlaying.value = false
      currentUrl.value = ''
    } else if (index < currentIndex.value) {
      // 如果删除的项在当前播放项之前，调整索引
      currentIndex.value--
    }
    playlist.value.splice(index, 1)

    // 如果列表为空，重置状态
    if (playlist.value.length === 0) {
      currentIndex.value = -1
      currentUrl.value = ''
      isPlaying.value = false
    }
  }

  const clearPlaylist = () => {
    playlist.value = []
    currentIndex.value = -1
    currentUrl.value = ''
    isPlaying.value = false
  }

  const playNext = () => {
    if (!hasPlaylist.value) return

    if (playMode.value === 'loop') {
      currentIndex.value = (currentIndex.value + 1) % playlist.value.length
    } else if (currentIndex.value < playlist.value.length - 1) {
      currentIndex.value++
    } else {
      return // 已经是最后一首
    }

    const track = playlist.value[currentIndex.value]
    if (track) {
      currentUrl.value = track.url
      track.played = true
    }
  }

  const playPrevious = () => {
    if (!hasPlaylist.value) return

    if (playMode.value === 'loop') {
      currentIndex.value = currentIndex.value <= 0
        ? playlist.value.length - 1
        : currentIndex.value - 1
    } else if (currentIndex.value > 0) {
      currentIndex.value--
    } else {
      return // 已经是第一首
    }

    const track = playlist.value[currentIndex.value]
    if (track) {
      currentUrl.value = track.url
      track.played = true
    }
  }

  const playTrack = (index: number) => {
    if (index >= 0 && index < playlist.value.length) {
      currentIndex.value = index
      const track = playlist.value[index]
      currentUrl.value = track.url
      // 标记为已播放
      track.played = true
    }
  }

  const setPlayMode = (mode: PlayMode) => {
    playMode.value = mode
  }

  const setPlaybackRate = (rate: number) => {
    playbackRate.value = rate
  }

  const updatePlaybackState = (state: PlaybackState) => {
    if (state.isPlaying !== undefined) isPlaying.value = state.isPlaying
    if (state.currentTime !== undefined) currentTime.value = state.currentTime
    if (state.duration !== undefined) duration.value = state.duration
    if (state.volume !== undefined) volume.value = state.volume
  }

  return {
    // 状态
    currentUrl,
    playlist,
    currentIndex,
    isPlaying,
    currentTime,
    duration,
    volume,
    playMode,
    playbackRate,
    // 计算属性
    hasPlaylist,
    canPlayNext,
    canPlayPrevious,
    currentTrack,
    // 动作
    setCurrentUrl,
    addToPlaylist,
    removeFromPlaylist,
    clearPlaylist,
    playNext,
    playPrevious,
    playTrack,
    setPlayMode,
    setPlaybackRate,
    updatePlaybackState
  }
})
