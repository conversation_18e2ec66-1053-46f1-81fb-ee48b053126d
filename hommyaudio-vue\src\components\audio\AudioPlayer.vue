<template>
  <div class="audio-player-container">
    <a-card :bordered="false" class="player-card">
      <!-- 音频元素 -->
      <audio
        ref="audioElement"
        @loadstart="handleLoadStart"
        @loadedmetadata="handleLoadedMetadata"
        @timeupdate="handleTimeUpdate"
        @ended="handleEnded"
        @error="handleError"
        @play="handlePlay"
        @pause="handlePause"
      />

      <!-- 当前播放信息 -->
      <div class="current-info" v-if="currentUrl">
        <a-typography-title :level="4" style="margin: 0 0 8px 0">
          {{ t('controls.now_playing') }}
        </a-typography-title>
        <a-typography-text type="secondary" class="current-url">
          {{ currentUrl }}
        </a-typography-text>
      </div>

      <!-- 进度条 -->
      <div class="progress-section" v-if="duration > 0">
        <div class="time-info">
          <span>{{ formatTime(currentTime) }}</span>
          <span>{{ formatTime(duration) }}</span>
        </div>
        <a-slider
          v-model="currentTime"
          :max="duration"
          :step="1"
          @change="handleSeek"
          class="progress-slider"
        />
      </div>

      <!-- 加载状态 - 只在长时间加载时显示 -->
      <div class="loading-section" v-if="loading">
        <a-spin :size="24">
          <template #tip>
            <div style="font-size: 14px">{{ t('controls.loading_audio') }}</div>
          </template>
        </a-spin>
      </div>

      <!-- 错误状态 -->
      <div class="error-section" v-if="error">
        <a-result status="error" :title="error" :sub-title="t('controls.check_url_network')">
          <template #extra>
            <a-button type="primary" @click="retry">{{ t('controls.retry') }}</a-button>
          </template>
        </a-result>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onUnmounted } from 'vue'
import { useI18n } from '@/composables/useI18n'

const { t } = useI18n()

const props = defineProps({
  currentUrl: {
    type: String,
    default: '',
  },
  autoPlay: {
    type: Boolean,
    default: false,
  },
  playbackRate: {
    type: Number,
    default: 1,
  },
})

const emit = defineEmits(['play', 'pause', 'ended', 'timeupdate', 'error'])

const audioElement = ref(null)
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(1)
const loading = ref(false)
const error = ref('')
const loadingTimer = ref(null)

// 监听URL变化
watch(
  () => props.currentUrl,
  async (newUrl, oldUrl) => {
    if (newUrl && audioElement.value) {
      // 清除之前的错误状态
      error.value = ''
      await loadAudio(newUrl)
    }
  },
  { flush: 'post' },
)

// 监听播放速度变化
watch(
  () => props.playbackRate,
  (newRate) => {
    setPlaybackRate(newRate)
  },
)

const loadAudio = async (url) => {
  if (!audioElement.value) return

  // 避免重复加载相同的URL
  if (audioElement.value.src === url) return

  // 不在这里设置loading，让handleLoadStart处理
  error.value = ''

  try {
    audioElement.value.src = url
    audioElement.value.load()

    // 确保每次加载都从头开始播放
    audioElement.value.currentTime = 0
    currentTime.value = 0

    // 重新设置播放速度（因为load()会重置所有属性）
    audioElement.value.playbackRate = props.playbackRate
    // 重新设置音量
    audioElement.value.volume = volume.value

    if (props.autoPlay) {
      await nextTick()
      await audioElement.value.play()
    }
  } catch (err) {
    loading.value = false
    error.value = t('controls.audio_load_failed')
    emit('error', err)
  }
}

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const handleLoadStart = () => {
  // 清除之前的定时器
  if (loadingTimer.value) {
    clearTimeout(loadingTimer.value)
  }

  // 使用较长延迟来避免快速切换时的闪烁
  loadingTimer.value = setTimeout(() => {
    if (audioElement.value && audioElement.value.readyState < 2) {
      loading.value = true
    }
  }, 800)
}

const handleLoadedMetadata = () => {
  // 清除loading定时器
  if (loadingTimer.value) {
    clearTimeout(loadingTimer.value)
    loadingTimer.value = null
  }

  loading.value = false
  duration.value = audioElement.value.duration || 0
  // 确保播放速度正确设置
  audioElement.value.playbackRate = props.playbackRate
}

const handleTimeUpdate = () => {
  currentTime.value = audioElement.value.currentTime || 0
  emit('timeupdate', currentTime.value)
}

const handlePlay = () => {
  isPlaying.value = true
  emit('play')
}

const handlePause = () => {
  isPlaying.value = false
  emit('pause')
}

const handleEnded = () => {
  isPlaying.value = false
  emit('ended')
}

const handleError = (event) => {
  loading.value = false

  // 根据错误类型提供更详细的错误信息
  const audio = event.target
  let errorMessage = '音频播放出错'

  if (audio.error) {
    switch (audio.error.code) {
      case audio.error.MEDIA_ERR_ABORTED:
        errorMessage = '音频加载被中止'
        break
      case audio.error.MEDIA_ERR_NETWORK:
        errorMessage = '网络错误，无法加载音频'
        break
      case audio.error.MEDIA_ERR_DECODE:
        errorMessage = '音频解码失败'
        break
      case audio.error.MEDIA_ERR_SRC_NOT_SUPPORTED:
        errorMessage = '音频格式不支持或URL无效'
        break
      default:
        errorMessage = '未知音频错误'
    }
  }

  error.value = errorMessage
  emit('error', event)
}

const handleSeek = (value) => {
  if (audioElement.value) {
    audioElement.value.currentTime = value
  }
}

const play = async () => {
  if (audioElement.value) {
    try {
      await audioElement.value.play()
    } catch (err) {
      // 检查是否是自动播放被阻止的错误
      if (err.name === 'NotAllowedError') {
        error.value = t('controls.autoplay_blocked')
      } else {
        error.value = t('controls.play_failed')
      }
      emit('error', err)
    }
  }
}

const pause = () => {
  if (audioElement.value) {
    audioElement.value.pause()
  }
}

const setVolume = (vol) => {
  volume.value = vol
  if (audioElement.value) {
    audioElement.value.volume = vol
  }
}

const setPlaybackRate = (rate) => {
  if (audioElement.value) {
    audioElement.value.playbackRate = rate
  }
}

const retry = () => {
  if (props.currentUrl) {
    loadAudio(props.currentUrl)
  }
}

const forceReload = () => {
  if (props.currentUrl) {
    loadAudio(props.currentUrl)
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (loadingTimer.value) {
    clearTimeout(loadingTimer.value)
  }
})

// 暴露方法给父组件
defineExpose({
  play,
  pause,
  setVolume,
  setPlaybackRate,
  audioElement,
  forceReload,
})
</script>

<style scoped>
.audio-player-container {
  margin-bottom: 24px;
}

.player-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 200px;
}

/* 深色模式下的播放器卡片强调 */
[arco-theme='dark'] .player-card {
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: var(--bg-primary);
}

.current-info {
  margin-bottom: 24px;
  text-align: center;
}

.current-url {
  word-break: break-all;
  display: block;
  margin-top: 8px;
}

.progress-section {
  margin: 24px 0;
}

.time-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #86909c;
}

.progress-slider {
  margin: 0;
}

.loading-section,
.error-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px;
}
</style>
