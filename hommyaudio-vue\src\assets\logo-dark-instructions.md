# 深色模式Logo说明

## 文件位置
请将深色模式的logo文件保存为：`src/assets/logo-dark.png`

## 设计要求
1. **尺寸**: 50x50 像素（与原logo相同）
2. **格式**: PNG格式，支持透明背景
3. **颜色**: 适合深色背景的浅色调设计
4. **风格**: 与原logo保持一致的设计风格

## 备选方案
如果没有专门的深色logo，系统会：
1. 首先尝试加载 `logo-dark.png`
2. 如果加载失败，自动回退到原logo `logo.png`
3. 在深色模式下对原logo应用CSS滤镜效果来提升可见性

## CSS滤镜效果
当前应用的滤镜效果：
```css
filter: brightness(1.1) contrast(0.9);
```

这会让logo在深色背景下更加清晰可见。

## 建议
为了最佳的用户体验，建议创建专门的深色模式logo文件。
