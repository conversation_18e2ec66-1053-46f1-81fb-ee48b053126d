#!/bin/bash

# HommyAudio 部署脚本
# 使用方法: ./deploy.sh

echo "🚀 开始部署 HommyAudio..."

# 1. 构建项目
echo "📦 构建项目..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi

echo "✅ 构建完成"

# 2. 检查 dist 目录
if [ ! -d "dist" ]; then
    echo "❌ dist 目录不存在"
    exit 1
fi

# 3. 显示版本信息（如果存在）
if [ -f "dist/version.json" ]; then
    echo "📋 版本信息:"
    cat dist/version.json
fi

# 4. 部署到服务器（根据您的实际情况修改）
echo "🔄 部署到服务器..."

# 方式1: 本地部署（测试用）
# cp -r dist/* /path/to/local/server/

# 方式2: 远程部署（修改为您的服务器信息）
# SERVER_HOST="your-server.com"
# SERVER_PATH="/var/www/hommyaudio"
# SERVER_USER="your-username"

# # 删除旧文件
# ssh $SERVER_USER@$SERVER_HOST "rm -rf $SERVER_PATH/*"

# # 上传新文件
# scp -r dist/* $SERVER_USER@$SERVER_HOST:$SERVER_PATH/

echo "✅ 部署完成！"
echo "🌐 用户访问网站时会自动检测到更新"
