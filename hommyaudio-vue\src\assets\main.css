@import './base.css';

/* HommyAudio 全局样式 */
:root {
  --primary-color: #165dff;
  --primary-hover: #0e42d2;
  --success-color: #00b42a;
  --warning-color: #ff7d00;
  --error-color: #f53f3f;
  --text-primary: #1d2129;
  --text-secondary: #4e5969;
  --text-disabled: #86909c;
  --bg-primary: #ffffff;
  --bg-secondary: #f7f8fa;
  --bg-tertiary: #e5e6eb;
  --border-color: #e5e6eb;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
  --border-radius: 6px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.5;
  transition: background-color 0.3s, color 0.3s;
}

/* 深色模式全局样式 */
[arco-theme='dark'] {
  --bg-primary: #17171a;
  --bg-secondary: #232324;
  --bg-tertiary: #2e2e30;
  --text-primary: #f7f8fa;
  --text-secondary: #c9cdd4;
  --text-disabled: #86909c;
  --border-color: #2e2e30;
}

/* 确保整个页面都是深色背景 */
html[arco-theme='dark'],
html[arco-theme='dark'] body {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

[arco-theme='dark'] body {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

#app {
  min-height: 100vh;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--text-disabled);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.pulse {
  animation: pulse 1.5s infinite;
}

/* 响应式断点 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.full-width {
  width: 100%;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.no-select {
  user-select: none;
}

/* Arco Design 主题定制 */
.arco-card {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: border 0.3s ease, box-shadow 0.3s ease;
}

/* 深色模式下的卡片边框强调 */
[arco-theme='dark'] .arco-card {
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[arco-theme='dark'] .arco-card:hover {
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

.arco-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.arco-btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}