import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import { webUpdateNotice } from '@plugin-web-update-notification/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    webUpdateNotice({
      logVersion: true,
      checkInterval: 5 * 60 * 1000, // 5分钟检查一次
      hiddenDefaultNotification: true, // 隐藏默认通知，使用自定义通知
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    port: 3000, // 固定端口
    host: true, // 允许外部访问
    fs: {
      // 允许访问项目根目录
      allow: ['..'],
    },
  },
})
