<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAudioStore } from './stores/counter'
import AppContainer from './components/layout/AppContainer.vue'
import UrlInput from './components/audio/UrlInput.vue'
import AudioPlayer from './components/audio/AudioPlayer.vue'
import AudioControls from './components/audio/AudioControls.vue'
import PlaylistManager from './components/audio/PlaylistManager.vue'
import Instructions from './components/common/Instructions.vue'
import ShortcutKeys from './components/common/ShortcutKeys.vue'
import UpdateNotification from './components/UpdateNotification.vue'
import { getAudioFromParams, updateUrlParams } from './utils/urlUtils'

const audioStore = useAudioStore()
const audioPlayerRef = ref(null)
const loading = ref(false)

// 使用computed来隔离响应式依赖，避免整个组件重新渲染
const currentUrl = computed({
  get: () => audioStore.currentUrl,
  set: (value) => audioStore.setCurrentUrl(value),
})
const isPlaying = computed(() => audioStore.isPlaying)
const hasPlaylist = computed(() => audioStore.hasPlaylist)
const canPlayPrevious = computed(() => audioStore.canPlayPrevious)
const canPlayNext = computed(() => audioStore.canPlayNext)
const volume = computed(() => audioStore.volume)
const playMode = computed(() => audioStore.playMode)
const playbackRate = computed(() => audioStore.playbackRate)
const playlist = computed(() => audioStore.playlist)
const currentIndex = computed(() => audioStore.currentIndex)

// 初始化 - 从URL参数加载音频
onMounted(() => {
  const { singleUrl, playlist } = getAudioFromParams()

  if (playlist.length > 0) {
    // 加载播放列表
    audioStore.addToPlaylist(playlist)
    audioStore.playTrack(0)
  } else if (singleUrl) {
    // 加载单个音频
    audioStore.setCurrentUrl(singleUrl)
  }

  // 添加全局键盘事件监听
  document.addEventListener('keydown', handleKeyDown)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})

// 键盘快捷键处理
const handleKeyDown = (event) => {
  // 如果焦点在输入框内，不处理快捷键
  const activeElement = document.activeElement
  if (
    activeElement &&
    (activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      activeElement.contentEditable === 'true')
  ) {
    return
  }

  switch (event.code) {
    case 'Space':
      event.preventDefault()
      if (isPlaying.value) {
        handlePause()
      } else {
        handlePlay()
      }
      break

    case 'ArrowLeft':
      event.preventDefault()
      // 快退5秒
      if (audioPlayerRef.value && audioPlayerRef.value.audioElement) {
        const audio = audioPlayerRef.value.audioElement
        audio.currentTime = Math.max(0, audio.currentTime - 5)
      }
      break

    case 'ArrowRight':
      event.preventDefault()
      // 快进5秒
      if (audioPlayerRef.value && audioPlayerRef.value.audioElement) {
        const audio = audioPlayerRef.value.audioElement
        audio.currentTime = Math.min(audio.duration || 0, audio.currentTime + 5)
      }
      break

    case 'ArrowUp':
      event.preventDefault()
      if (audioPlayerRef.value) {
        // 使用整数计算避免浮点数精度问题
        const currentVolumePercent = Math.round(volume.value * 100)
        const newVolumePercent = Math.min(100, currentVolumePercent + 10)
        const newVolume = newVolumePercent / 100
        audioPlayerRef.value.setVolume(newVolume)
        audioStore.updatePlaybackState({ volume: newVolume })
      }
      break

    case 'ArrowDown':
      event.preventDefault()
      if (audioPlayerRef.value) {
        // 使用整数计算避免浮点数精度问题
        const currentVolumePercent = Math.round(volume.value * 100)
        const newVolumePercent = Math.max(0, currentVolumePercent - 10)
        const newVolume = newVolumePercent / 100
        audioPlayerRef.value.setVolume(newVolume)
        audioStore.updatePlaybackState({ volume: newVolume })
      }
      break
  }
}

// 处理单个URL播放
const handleUrlSubmit = (url) => {
  loading.value = true

  // 清理和验证URL
  let cleanedUrl = url.trim()

  // 检查是否是包含URL参数的localhost地址
  if (cleanedUrl.includes('localhost') && cleanedUrl.includes('?url=')) {
    try {
      const urlObj = new URL(cleanedUrl)
      const urlParam = urlObj.searchParams.get('url')
      if (urlParam) {
        cleanedUrl = decodeURIComponent(urlParam)
      }
    } catch (error) {
      console.warn('URL解析失败:', error)
    }
  }

  audioStore.setCurrentUrl(cleanedUrl)

  setTimeout(() => {
    loading.value = false
  }, 1000)
}

// 处理批量URL添加
const handleBatchSubmit = (urls) => {
  audioStore.addToPlaylist(urls)

  // 如果当前没有播放，自动播放第一个
  if (!audioStore.currentUrl && audioStore.playlist.length > 0) {
    audioStore.playTrack(0)
  }
}

// 播放控制
const handlePlay = () => {
  if (audioPlayerRef.value) {
    audioPlayerRef.value.play()
  }
}

const handlePause = () => {
  if (audioPlayerRef.value) {
    audioPlayerRef.value.pause()
  }
}

const handlePrevious = () => {
  if (canPlayPrevious.value) {
    audioStore.playPrevious()
  }
}

const handleNext = () => {
  if (canPlayNext.value) {
    audioStore.playNext()
  }
}

const handleVolumeChange = (volume) => {
  if (audioPlayerRef.value) {
    audioPlayerRef.value.setVolume(volume)
  }
  audioStore.updatePlaybackState({ volume })
}

const handleModeChange = (mode) => {
  audioStore.setPlayMode(mode)
}

const handleSpeedChange = (speed) => {
  audioStore.setPlaybackRate(speed)
  if (audioPlayerRef.value) {
    audioPlayerRef.value.setPlaybackRate(speed)
  }
}

// 播放列表控制
const handlePlaylistItemClick = (index) => {
  audioStore.playTrack(index)
}

const handlePlaylistItemRemove = (index) => {
  audioStore.removeFromPlaylist(index)
}

const handlePlaylistClear = () => {
  audioStore.clearPlaylist()
}

// 音频播放器事件
const handleAudioPlay = () => {
  audioStore.updatePlaybackState({ isPlaying: true })
}

const handleAudioPause = () => {
  audioStore.updatePlaybackState({ isPlaying: false })
}

const handleAudioEnded = () => {
  audioStore.updatePlaybackState({ isPlaying: false })

  // 自动播放下一首
  if (playMode.value === 'list' || playMode.value === 'loop') {
    if (canPlayNext.value) {
      setTimeout(() => {
        audioStore.playNext()
      }, 500)
    }
  }
}

const handleTimeUpdate = (currentTime) => {
  audioStore.updatePlaybackState({ currentTime })
}
</script>

<template>
  <AppContainer>
    <!-- URL输入区域 -->
    <UrlInput
      v-model="currentUrl"
      :loading="loading"
      @submit="handleUrlSubmit"
      @batch-submit="handleBatchSubmit"
    />

    <!-- 音频播放器 -->
    <AudioPlayer
      key="main-audio-player"
      ref="audioPlayerRef"
      :current-url="currentUrl"
      :auto-play="true"
      :playback-rate="playbackRate"
      @play="handleAudioPlay"
      @pause="handleAudioPause"
      @ended="handleAudioEnded"
      @timeupdate="handleTimeUpdate"
    />

    <!-- 音频控制按钮 -->
    <AudioControls
      :is-playing="isPlaying"
      :has-playlist="hasPlaylist"
      :can-previous="canPlayPrevious"
      :can-next="canPlayNext"
      :volume="volume"
      :play-mode="playMode"
      :playback-rate="playbackRate"
      @play="handlePlay"
      @pause="handlePause"
      @previous="handlePrevious"
      @next="handleNext"
      @volume-change="handleVolumeChange"
      @mode-change="handleModeChange"
      @speed-change="handleSpeedChange"
    />

    <!-- 快捷键说明 -->
    <ShortcutKeys />

    <!-- 播放列表 -->
    <PlaylistManager
      :playlist="playlist"
      :current-index="currentIndex"
      @item-click="handlePlaylistItemClick"
      @item-remove="handlePlaylistItemRemove"
      @list-clear="handlePlaylistClear"
    />

    <!-- 使用说明 -->
    <Instructions />

    <!-- 自定义更新通知 -->
    <UpdateNotification />
  </AppContainer>
</template>

<style>
/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f7f8fa;
}

#app {
  min-height: 100vh;
}
</style>
