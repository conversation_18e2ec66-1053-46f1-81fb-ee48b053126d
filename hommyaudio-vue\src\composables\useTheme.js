import { ref, watch, computed } from 'vue'

// 主题模式：'light' | 'dark' | 'auto'
const themeMode = ref('auto')
const isDark = ref(false)

// 从localStorage读取主题设置
const savedTheme = localStorage.getItem('hommyaudio-theme')
if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
  themeMode.value = savedTheme
} else {
  themeMode.value = 'auto'
}

// 检测系统主题偏好
const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)')

// 计算实际主题
const computeActualTheme = () => {
  if (themeMode.value === 'auto') {
    return systemPrefersDark.matches
  }
  return themeMode.value === 'dark'
}

// 应用主题
const applyTheme = (dark) => {
  if (dark) {
    document.body.setAttribute('arco-theme', 'dark')
    document.documentElement.setAttribute('arco-theme', 'dark')
    document.documentElement.classList.add('dark')
  } else {
    document.body.removeAttribute('arco-theme')
    document.documentElement.removeAttribute('arco-theme')
    document.documentElement.classList.remove('dark')
  }
}

// 初始化主题
isDark.value = computeActualTheme()
applyTheme(isDark.value)

// 监听系统主题变化
systemPrefersDark.addEventListener('change', () => {
  if (themeMode.value === 'auto') {
    isDark.value = systemPrefersDark.matches
    applyTheme(isDark.value)
  }
})

// 监听主题模式变化
watch(themeMode, (newMode) => {
  isDark.value = computeActualTheme()
  applyTheme(isDark.value)
  localStorage.setItem('hommyaudio-theme', newMode)
})

// 主题选项
const themeOptions = [
  { label: '浅色模式', value: 'light', icon: 'icon-sun' },
  { label: '深色模式', value: 'dark', icon: 'icon-moon' },
  { label: '跟随系统', value: 'auto', icon: 'icon-computer' },
]

export const useTheme = () => {
  const setThemeMode = (mode) => {
    themeMode.value = mode
  }

  // 简单的主题切换：浅色 <-> 深色
  const toggleTheme = () => {
    if (themeMode.value === 'light') {
      setThemeMode('dark')
    } else if (themeMode.value === 'dark') {
      setThemeMode('light')
    } else {
      // 如果是auto模式，根据当前实际主题切换
      setThemeMode(isDark.value ? 'light' : 'dark')
    }
  }

  const currentThemeLabel = computed(() => {
    const option = themeOptions.find((opt) => opt.value === themeMode.value)
    return option?.label || '跟随系统'
  })

  return {
    themeMode,
    isDark,
    themeOptions,
    currentThemeLabel,
    setThemeMode,
    toggleTheme,
  }
}
