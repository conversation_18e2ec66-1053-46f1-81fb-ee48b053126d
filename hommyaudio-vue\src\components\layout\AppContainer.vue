<template>
  <a-layout class="app-layout">
    <AppHeader />
    <a-layout-content class="main-content">
      <div class="content-container">
        <slot />
      </div>
    </a-layout-content>
    <AppFooter />
  </a-layout>
</template>

<script setup>
import AppHeader from './AppHeader.vue'
import AppFooter from './AppFooter.vue'
</script>

<style scoped>
.app-layout {
  min-height: 100vh;
  background: var(--bg-secondary);
  transition: background-color 0.3s;
}

.main-content {
  padding: 104px 0 40px 0; /* 64px navbar高度 + 40px间距 */
  background: var(--bg-secondary);
  transition: background-color 0.3s;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 深色模式样式 */
[arco-theme='dark'] .app-layout,
[arco-theme='dark'] .main-content {
  background: var(--bg-secondary);
}
</style>
