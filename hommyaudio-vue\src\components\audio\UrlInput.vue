<template>
  <div class="url-input-container">
    <a-card :title="t('audio.title')" :bordered="false" class="input-card">
      <div class="input-section">
        <a-space direction="vertical" size="large" style="width: 100%">
          <!-- 单个URL输入 -->
          <div v-if="!batchMode">
            <a-input-group class="url-input-group">
              <a-input
                v-model="singleUrl"
                :placeholder="t('audio.placeholder')"
                size="large"
                @keypress="handleKeyPress"
                :status="urlStatus"
                class="url-input"
              />
              <a-button
                type="primary"
                size="large"
                @click="handleSubmit"
                :loading="loading"
                class="play-button"
              >
                {{ t('audio.play') }}
              </a-button>
            </a-input-group>
          </div>

          <!-- 批量URL输入 -->
          <div v-else class="batch-input-section">
            <a-textarea
              v-model="batchUrls"
              :placeholder="t('playlist.batch_placeholder')"
              :rows="6"
              :auto-size="false"
              allow-clear
              @keypress="handleKeyPress"
              class="batch-textarea"
            />
            <a-button
              type="primary"
              size="large"
              @click="handleBatchSubmit"
              :loading="loading"
              class="batch-submit-button"
            >
              {{ t('playlist.add') }}
            </a-button>
          </div>

          <!-- 模式切换 -->
          <div class="mode-switch">
            <a-switch
              v-model="batchMode"
              :checked-text="t('mode.batch')"
              :unchecked-text="t('mode.single_mode')"
            />
          </div>
        </a-space>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from '@/composables/useI18n'

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'submit', 'batch-submit'])

const { t } = useI18n()
const batchMode = ref(false)
const singleUrl = ref(props.modelValue)
const batchUrls = ref('')

const urlStatus = computed(() => {
  if (!singleUrl.value) return undefined
  return validateUrl(singleUrl.value) ? 'success' : 'error'
})

const validateUrl = (url) => {
  if (!url) return false
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

const handleKeyPress = (event) => {
  if (event.key === 'Enter' && !batchMode.value) {
    handleSubmit()
  }
}

const handleSubmit = () => {
  if (validateUrl(singleUrl.value)) {
    emit('update:modelValue', singleUrl.value)
    emit('submit', singleUrl.value)
  }
}

const handleBatchSubmit = () => {
  const urls = batchUrls.value
    .split('\n')
    .map((url) => url.trim())
    .filter((url) => url && validateUrl(url))

  if (urls.length > 0) {
    emit('batch-submit', urls)
    batchUrls.value = ''
  }
}
</script>

<style scoped>
.url-input-container {
  margin-bottom: 24px;
}

.input-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 100%;
}

/* 深色模式下的输入卡片强调 */
[arco-theme='dark'] .input-card {
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: var(--bg-primary);
}

.input-section {
  padding: 8px 0;
}

.url-input-group {
  display: flex;
  width: 100%;
}

.url-input {
  flex: 1;
  min-width: 0; /* 允许输入框收缩 */
}

.play-button {
  flex-shrink: 0;
  min-width: 80px;
}

.batch-input-section {
  width: 100%;
}

.batch-textarea {
  width: 100%;
  min-height: 150px;
}

/* 确保Arco Design的textarea支持resize */
.batch-textarea :deep(.arco-textarea) {
  resize: vertical !important;
  min-height: 150px !important;
  max-height: none !important;
}

/* 确保textarea wrapper也支持resize */
.batch-textarea :deep(.arco-textarea-wrapper) {
  resize: vertical !important;
  min-height: 150px !important;
}

.batch-submit-button {
  margin-top: 12px;
  width: 100%;
}

.mode-switch {
  display: flex;
  justify-content: center;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .url-input-group {
    flex-direction: column;
    gap: 12px;
  }

  .play-button {
    width: 100%;
    min-width: auto;
  }

  .batch-submit-button {
    margin-top: 16px;
  }
}
</style>
