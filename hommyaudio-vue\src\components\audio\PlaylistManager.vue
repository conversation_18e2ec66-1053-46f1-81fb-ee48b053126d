<template>
  <div class="playlist-container" v-if="playlist.length > 0">
    <a-card :bordered="false" class="playlist-card">
      <template #title>
        <div class="playlist-header">
          <div class="playlist-title">
            <IconMusic />
            <span>{{ t('playlist.title') }}</span>
            <a-badge :count="playlist.length" :max-count="99" class="playlist-badge" />
          </div>
          <a-button
            size="small"
            @click="clearPlaylist"
            type="text"
            status="danger"
            class="clear-button"
          >
            {{ t('playlist.clear') }}
          </a-button>
        </div>
      </template>

      <div class="playlist-content">
        <a-list :data="playlist" :virtual-list-props="{ height: 300 }" class="playlist-list">
          <template #item="{ item, index }">
            <a-list-item
              :class="{ 'current-item': index === currentIndex }"
              @click="handleItemClick(index)"
              class="playlist-item"
            >
              <template #actions>
                <a-button
                  size="mini"
                  type="text"
                  status="danger"
                  @click.stop="handleItemRemove(index)"
                  :icon="h(IconDelete)"
                />
              </template>

              <a-list-item-meta>
                <template #avatar>
                  <div class="item-index" :class="getItemIndexClass(item, index)">
                    <span v-if="index === currentIndex" class="playing-indicator">
                      <IconSound />
                    </span>
                    <span v-else-if="item.played" class="played-indicator">
                      <IconCheckCircle />
                    </span>
                    <span v-else class="unplayed-indicator">{{ index + 1 }}</span>
                  </div>
                </template>

                <template #title>
                  <div class="item-title" :class="getItemTitleClass(item, index)">
                    {{ item.title || `${t('playlist.audio_item')} ${index + 1}` }}
                    <a-tag v-if="index === currentIndex" color="blue" size="small" class="status-tag">
                      {{ t('playlist.playing') }}
                    </a-tag>
                    <a-tag v-else-if="item.played" color="green" size="small" class="status-tag">
                      {{ t('playlist.played') }}
                    </a-tag>
                  </div>
                </template>

                <template #description>
                  <div class="item-url">{{ item.url }}</div>
                  <div class="item-duration" v-if="item.duration">
                    {{ t('playlist.duration') }}: {{ formatTime(item.duration) }}
                  </div>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { h } from 'vue'
import { IconMusic, IconDelete, IconSound, IconCheckCircle } from '@arco-design/web-vue/es/icon'
import { useI18n } from '@/composables/useI18n'

const props = defineProps({
  playlist: {
    type: Array,
    default: () => [],
  },
  currentIndex: {
    type: Number,
    default: -1,
  },
})

const emit = defineEmits(['item-click', 'item-remove', 'list-clear'])

const { t } = useI18n()

const formatTime = (seconds) => {
  if (!seconds) return t('playlist.unknown')
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const handleItemClick = (index) => {
  emit('item-click', index)
}

const handleItemRemove = (index) => {
  emit('item-remove', index)
}

const clearPlaylist = () => {
  emit('list-clear')
}

const getItemIndexClass = (item, index) => {
  if (index === props.currentIndex) return 'current-playing'
  if (item.played) return 'played'
  return 'unplayed'
}

const getItemTitleClass = (item, index) => {
  if (index === props.currentIndex) return 'current-title'
  if (item.played) return 'played-title'
  return 'unplayed-title'
}
</script>

<style scoped>
.playlist-container {
  margin-bottom: 24px;
}

.playlist-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.playlist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.playlist-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.playlist-badge {
  margin-left: 4px;
}

.clear-button {
  flex-shrink: 0;
}

/* 深色模式下的播放列表卡片强调 */
[arco-theme='dark'] .playlist-card {
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: var(--bg-primary);
}

.playlist-content {
  max-height: 400px;
  overflow-y: auto;
}

.playlist-item {
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 6px;
  margin-bottom: 4px;
  padding: 8px;
}

.playlist-item:hover {
  background-color: #f7f8fa;
}

.playlist-item.current-item {
  background-color: #e8f4ff;
  border: 1px solid #165dff;
}

.item-index {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f2f3f5;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 500;
}

.current-item .item-index {
  background: #165dff;
  color: white;
}

.playing-indicator {
  color: #165dff;
  animation: pulse 1.5s infinite;
}

.current-item .playing-indicator {
  color: white;
}

.played-indicator {
  color: #00b42a;
  font-size: 14px;
}

.unplayed-indicator {
  color: #86909c;
}

.item-index.current-playing {
  background: #165dff;
  color: white;
}

.item-index.played {
  background: rgba(0, 180, 42, 0.1);
  color: #00b42a;
  border: 1px solid #00b42a;
}

.item-index.unplayed {
  background: #f2f3f5;
  color: #86909c;
}

.item-title.current-title {
  color: #165dff;
  font-weight: 600;
}

.item-title.played-title {
  color: #00b42a;
  font-weight: 500;
}

.item-title.unplayed-title {
  color: var(--text-primary);
}

.status-tag {
  margin-left: 8px;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.item-title {
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 4px;
}

.item-url {
  font-size: 12px;
  color: #86909c;
  word-break: break-all;
  margin-bottom: 2px;
}

.item-duration {
  font-size: 12px;
  color: #4e5969;
}

.playlist-list {
  padding: 0;
}
</style>
