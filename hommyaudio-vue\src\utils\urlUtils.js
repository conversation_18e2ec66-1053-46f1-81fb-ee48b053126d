/**
 * URL处理工具函数
 */

/**
 * 解析URL参数
 */
export const parseUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  const result = {}
  
  for (const [key, value] of params) {
    result[key] = decodeURIComponent(value)
  }
  
  return result
}

/**
 * 更新URL参数 (不刷新页面)
 */
export const updateUrlParams = (params) => {
  const url = new URL(window.location)
  
  // 清除现有参数
  url.search = ''
  
  // 添加新参数
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      url.searchParams.set(key, encodeURIComponent(value))
    }
  })
  
  // 更新浏览器历史记录
  window.history.pushState({}, '', url)
}

/**
 * 编码音频URL
 */
export const encodeAudioUrl = (url) => {
  try {
    return encodeURIComponent(url)
  } catch (error) {
    console.warn('URL编码失败:', error)
    return url
  }
}

/**
 * 解码音频URL
 */
export const decodeAudioUrl = (url) => {
  try {
    return decodeURIComponent(url)
  } catch (error) {
    console.warn('URL解码失败:', error)
    return url
  }
}

/**
 * 批量解析URL文本
 */
export const batchParseUrls = (text) => {
  if (!text || typeof text !== 'string') return []
  
  const lines = text.split('\n')
  const urls = []
  
  lines.forEach(line => {
    const trimmed = line.trim()
    if (trimmed && isValidUrl(trimmed)) {
      urls.push(trimmed)
    }
  })
  
  // 去重
  return [...new Set(urls)]
}

/**
 * 验证URL格式
 */
export const isValidUrl = (string) => {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}

/**
 * 从URL参数中获取音频信息
 */
export const getAudioFromParams = () => {
  const params = parseUrlParams()
  const result = {
    singleUrl: null,
    playlist: []
  }
  
  // 单个音频URL
  if (params.url) {
    result.singleUrl = params.url
  }
  
  // 播放列表
  if (params.playlist) {
    try {
      const urls = params.playlist.split(',').map(url => url.trim())
      result.playlist = urls.filter(url => isValidUrl(url))
    } catch (error) {
      console.warn('播放列表解析失败:', error)
    }
  }
  
  return result
}

/**
 * 生成分享链接
 */
export const generateShareLink = (audioUrl, playlist = null) => {
  const baseUrl = window.location.origin + window.location.pathname
  const params = {}
  
  if (playlist && playlist.length > 0) {
    // 播放列表分享
    const urls = playlist.map(track => track.url).join(',')
    params.playlist = urls
  } else if (audioUrl) {
    // 单个音频分享
    params.url = audioUrl
  }
  
  if (Object.keys(params).length === 0) {
    return baseUrl
  }
  
  const searchParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    searchParams.set(key, encodeURIComponent(value))
  })
  
  return `${baseUrl}?${searchParams.toString()}`
}

/**
 * 复制文本到剪贴板
 */
export const copyToClipboard = async (text) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      return true
    } else {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      const result = document.execCommand('copy')
      document.body.removeChild(textArea)
      return result
    }
  } catch (error) {
    console.error('复制失败:', error)
    return false
  }
}

/**
 * 清理URL (移除追踪参数等)
 */
export const cleanUrl = (url) => {
  try {
    const urlObj = new URL(url)
    
    // 移除常见的追踪参数
    const trackingParams = [
      'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
      'fbclid', 'gclid', 'ref', 'source'
    ]
    
    trackingParams.forEach(param => {
      urlObj.searchParams.delete(param)
    })
    
    return urlObj.toString()
  } catch (error) {
    return url
  }
}

/**
 * 获取URL的域名
 */
export const getDomain = (url) => {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch (error) {
    return 'unknown'
  }
}

/**
 * 检查URL是否为HTTPS
 */
export const isHttps = (url) => {
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'https:'
  } catch (error) {
    return false
  }
}
