<template>
  <div class="shortcuts-container">
    <a-card :title="t('instructions.shortcuts')" :bordered="false" class="shortcuts-card">
      <a-descriptions :column="2" size="small" bordered>
        <a-descriptions-item :label="t('shortcuts.space_key')">
          {{ t('shortcuts.space') }}
        </a-descriptions-item>
        <a-descriptions-item :label="t('shortcuts.enter_key')">
          {{ t('shortcuts.enter') }}
        </a-descriptions-item>
        <a-descriptions-item :label="t('shortcuts.left_key')">
          {{ t('shortcuts.left') }}
        </a-descriptions-item>
        <a-descriptions-item :label="t('shortcuts.right_key')">
          {{ t('shortcuts.right') }}
        </a-descriptions-item>
        <a-descriptions-item :label="t('shortcuts.up_key')">
          {{ t('shortcuts.up') }}
        </a-descriptions-item>
        <a-descriptions-item :label="t('shortcuts.down_key')">
          {{ t('shortcuts.down') }}
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
  </div>
</template>

<script setup>
import { useI18n } from '@/composables/useI18n'

const { t } = useI18n()
</script>

<style scoped>
.shortcuts-container {
  margin-bottom: 24px;
}

.shortcuts-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 深色模式下的快捷键卡片强调 */
[arco-theme='dark'] .shortcuts-card {
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: var(--bg-primary);
}
</style>
