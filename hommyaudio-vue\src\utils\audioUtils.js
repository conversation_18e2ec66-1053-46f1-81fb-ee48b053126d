/**
 * 音频工具函数
 */

/**
 * 格式化时间 (秒 -> mm:ss)
 */
export const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '0:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

/**
 * 检测音频格式
 */
export const detectAudioFormat = (url) => {
  if (!url) return 'unknown'
  
  // 标准音频格式
  const formatMatch = url.match(/\.([a-zA-Z0-9]+)(?:\?|$)/)
  if (formatMatch) {
    const ext = formatMatch[1].toLowerCase()
    const audioFormats = ['mp3', 'wav', 'ogg', 'm4a', 'flac', 'webm', 'aac']
    if (audioFormats.includes(ext)) {
      return ext
    }
  }
  
  // 特殊平台检测
  if (url.includes('nie.netease.com')) return 'netease'
  if (url.includes('qq.com')) return 'qq'
  if (url.includes('kugou.com')) return 'kugou'
  
  return 'unknown'
}

/**
 * 验证音频URL
 */
export const validateAudioUrl = (url) => {
  if (!url || typeof url !== 'string') return false
  
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 提取音频信息 (异步)
 */
export const extractAudioInfo = async (url) => {
  return new Promise((resolve) => {
    const audio = new Audio()
    
    const cleanup = () => {
      audio.removeEventListener('loadedmetadata', onLoaded)
      audio.removeEventListener('error', onError)
      audio.src = ''
    }
    
    const onLoaded = () => {
      const info = {
        duration: audio.duration || 0,
        format: detectAudioFormat(url),
        canPlay: true
      }
      cleanup()
      resolve(info)
    }
    
    const onError = () => {
      const info = {
        duration: 0,
        format: detectAudioFormat(url),
        canPlay: false
      }
      cleanup()
      resolve(info)
    }
    
    audio.addEventListener('loadedmetadata', onLoaded)
    audio.addEventListener('error', onError)
    
    // 设置超时
    setTimeout(() => {
      cleanup()
      resolve({
        duration: 0,
        format: detectAudioFormat(url),
        canPlay: false
      })
    }, 5000)
    
    audio.src = url
  })
}

/**
 * 生成分享URL
 */
export const generateShareUrl = (url, playlist = null) => {
  const baseUrl = window.location.origin + window.location.pathname
  const params = new URLSearchParams()
  
  if (playlist && playlist.length > 0) {
    // 批量分享
    const urls = playlist.map(track => track.url).join(',')
    params.set('playlist', encodeURIComponent(urls))
  } else if (url) {
    // 单个分享
    params.set('url', encodeURIComponent(url))
  }
  
  return params.toString() ? `${baseUrl}?${params.toString()}` : baseUrl
}

/**
 * 检查浏览器音频支持
 */
export const checkAudioSupport = () => {
  const audio = new Audio()
  const support = {
    mp3: !!audio.canPlayType('audio/mpeg'),
    wav: !!audio.canPlayType('audio/wav'),
    ogg: !!audio.canPlayType('audio/ogg'),
    m4a: !!audio.canPlayType('audio/mp4'),
    webm: !!audio.canPlayType('audio/webm')
  }
  
  return support
}

/**
 * 获取音频元数据
 */
export const getAudioMetadata = async (url) => {
  try {
    const response = await fetch(url, { method: 'HEAD' })
    const contentType = response.headers.get('content-type')
    const contentLength = response.headers.get('content-length')
    
    return {
      contentType,
      size: contentLength ? parseInt(contentLength) : null,
      accessible: response.ok
    }
  } catch (error) {
    return {
      contentType: null,
      size: null,
      accessible: false,
      error: error.message
    }
  }
}

/**
 * 音频预加载
 */
export const preloadAudio = (url) => {
  return new Promise((resolve, reject) => {
    const audio = new Audio()
    
    audio.addEventListener('canplaythrough', () => {
      resolve(audio)
    })
    
    audio.addEventListener('error', (error) => {
      reject(error)
    })
    
    audio.preload = 'auto'
    audio.src = url
  })
}
