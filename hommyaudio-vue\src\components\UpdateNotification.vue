<template>
  <!-- 使用 Arco Design 的 Notification 组件 -->
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import { Notification } from '@arco-design/web-vue'

let notificationInstance = null

const showNotification = (event) => {
  const { version: newVersion } = event.detail

  // 如果已有通知，先关闭
  if (notificationInstance) {
    notificationInstance.close()
  }

  // 显示 Arco Design 通知
  notificationInstance = Notification.info({
    title: '🚀 HommyAudio 有新版本啦！',
    content: '我们刚刚发布了新功能和优化，点击更新即可体验最新版本。',
    position: 'topRight',
    duration: 0, // 不自动关闭
    closable: true,
    showIcon: true,
    action: [
      {
        content: '稍后再说',
        style: { marginRight: '12px' },
        onClick: () => {
          notificationInstance?.close()
          // 调用插件的 dismiss 方法
          if (window.pluginWebUpdateNotice_) {
            window.pluginWebUpdateNotice_.dismissUpdate()
          }
        },
      },
      {
        content: '立即更新',
        type: 'primary',
        onClick: () => {
          window.location.reload()
        },
      },
    ],
    onClose: () => {
      // 调用插件的 dismiss 方法
      if (window.pluginWebUpdateNotice_) {
        window.pluginWebUpdateNotice_.dismissUpdate()
      }
    },
  })
}

onMounted(() => {
  // 监听插件的自定义更新事件
  document.body.addEventListener('plugin_web_update_notice', showNotification)
})

onUnmounted(() => {
  document.body.removeEventListener('plugin_web_update_notice', showNotification)
  // 清理通知实例
  if (notificationInstance) {
    notificationInstance.close()
  }
})
</script>
