<template>
  <a-layout-footer class="app-footer">
    <div class="footer-content">
      <div class="footer-info">
        <span class="copyright">{{ t('footer.copyright') }}</span>
        <span class="separator">|</span>
        <a
          href="https://beian.miit.gov.cn/"
          target="_blank"
          rel="noopener noreferrer"
          class="icp-link"
        >
          {{ t('footer.icp') }}
        </a>
      </div>
    </div>
  </a-layout-footer>
</template>

<script setup>
import { useI18n } from '@/composables/useI18n'

const { t } = useI18n()
</script>

<style scoped>
.app-footer {
  background: #f7f8fa;
  border-top: 1px solid #e5e6eb;
  padding: 24px 0;
  margin-top: 40px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.copyright {
  color: #4e5969;
  margin: 0;
}

.separator {
  color: #86909c;
}

.icp-link {
  color: #86909c;
  text-decoration: none;
  transition: color 0.2s;
}

.icp-link:hover {
  color: #165dff;
}

.footer-links {
  display: flex;
  align-items: center;
}

@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-info {
    order: 2;
  }

  .footer-links {
    order: 1;
  }
}

/* 深色模式样式 */
[arco-theme='dark'] .app-footer {
  background: #17171a;
  border-top-color: #2e2e30;
}

[arco-theme='dark'] .copyright {
  color: #c9cdd4;
}

[arco-theme='dark'] .separator {
  color: #86909c;
}

[arco-theme='dark'] .icp-link {
  color: #86909c;
}

[arco-theme='dark'] .icp-link:hover {
  color: #4080ff;
}
</style>
