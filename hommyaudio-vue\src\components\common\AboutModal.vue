<template>
  <a-modal
    v-model:visible="visible"
    :title="t('about.title')"
    :width="600"
    :footer="false"
    @cancel="handleClose"
  >
    <div class="about-content">
      <div class="about-section">
        <div class="app-info">
          <div class="app-logo">
            <img src="@/assets/logo.png" alt="HommyAudio Logo" class="logo" />
          </div>
          <div class="app-details">
            <h2 class="app-name">HommyAudio</h2>
            <p class="app-description">{{ t('about.description') }}</p>
          </div>
        </div>
      </div>

      <a-divider />

      <div class="about-section">
        <h3>{{ t('about.features') }}</h3>
        <a-list size="small">
          <a-list-item>
            <IconCheckCircle class="feature-icon" />
            {{ t('about.feature1') }}
          </a-list-item>
          <a-list-item>
            <IconCheckCircle class="feature-icon" />
            {{ t('about.feature2') }}
          </a-list-item>
          <a-list-item>
            <IconCheckCircle class="feature-icon" />
            {{ t('about.feature3') }}
          </a-list-item>
          <a-list-item>
            <IconCheckCircle class="feature-icon" />
            {{ t('about.feature4') }}
          </a-list-item>
          <a-list-item>
            <IconCheckCircle class="feature-icon" />
            {{ t('about.feature5') }}
          </a-list-item>
        </a-list>
      </div>

      <a-divider />

      <div class="about-section">
        <h3>{{ t('about.tech_stack') }}</h3>
        <div class="tech-tags">
          <a-tag color="blue">Vue 3</a-tag>
          <a-tag color="green">Arco Design</a-tag>
          <a-tag color="orange">Vite</a-tag>
          <a-tag color="purple">Pinia</a-tag>
          <a-tag color="cyan">TypeScript</a-tag>
        </div>
      </div>

      <a-divider />

      <div class="about-section">
        <h3>{{ t('about.contact') }}</h3>
        <div class="contact-info">
          <p>
            <IconEmail class="contact-icon" />
            <span>{{ t('about.email') }}: </span>
            <a href="mailto:<EMAIL>">&nbsp;<EMAIL></a>
          </p>
        </div>
      </div>

      <a-divider />

      <div class="about-section">
        <div class="copyright">
          <p>{{ t('footer.copyright') }}</p>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue'
import { IconCheckCircle, IconEmail } from '@arco-design/web-vue/es/icon'
import { useI18n } from '@/composables/useI18n'

const { t } = useI18n()

const visible = ref(false)

const show = () => {
  visible.value = true
}

const handleClose = () => {
  visible.value = false
}

defineExpose({
  show,
})
</script>

<style scoped>
.about-content {
  padding: 16px 0;
}

.about-section {
  margin-bottom: 24px;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 24px;
}

.app-logo .logo {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  object-fit: contain;
}

.app-details {
  flex: 1;
}

.app-name {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.app-version {
  margin: 0 0 12px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.app-description {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

.feature-icon {
  color: #00b42a;
  margin-right: 8px;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.contact-info p {
  display: flex;
  align-items: center;
  margin: 8px 0;
}

.contact-icon {
  margin-right: 8px;
  color: var(--text-secondary);
}

.contact-info a {
  color: var(--primary-color);
  text-decoration: none;
}

.contact-info a:hover {
  text-decoration: underline;
}

.copyright {
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
}

.license {
  margin-top: 8px;
  font-size: 12px;
}

/* 深色模式适配 */
[arco-theme='dark'] .app-name {
  color: var(--text-primary);
}

[arco-theme='dark'] .contact-info a {
  color: #4080ff;
}
</style>
